FROM harbor.hgj.net/library/yunlsp-python3.10.10-chrome:1.2
# 构建默认值
ARG SERVICE_NAME
ARG BUILD_ENV
# 服务名称
ENV APP_NAME $SERVICE_NAME
# 当前运行环境
ENV SERVER_ENV $BUILD_ENV
ADD . $APP_NAME
WORKDIR /$APP_NAME

RUN mv ./plugins/ffmpeg-release-amd64-static.tar.xz /usr/local/share/ && cd /usr/local/share/ && xz -d ffmpeg-release-amd64-static.tar.xz && tar -xvf ffmpeg-release-amd64-static.tar && ln -s /usr/local/share/ffmpeg-6.0-amd64-static/ffmpeg /usr/local/bin/ffmpeg

RUN  mkdir logs && /usr/local/bin/python3.10 -m pip install --upgrade pip -i https://pypi.douban.com/simple && pip3 install -r requirements.txt && cp ./docs/my_python_path.pth /usr/local/lib/python3.10/site-packages/


CMD ["/bin/bash", "-c", "set -e && gunicorn main:app"]
