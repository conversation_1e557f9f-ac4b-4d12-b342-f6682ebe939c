"""
创建应用对象
"""
import logging
import secrets

from flask import Flask
from utils.util_logger import logger


class Config(object):
    SECRET_KEY = secrets.token_hex()
    JSON_AS_ASCII = False               # 防止中文转换


def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    access_logger = logging.getLogger("gunicorn.access")
    app.logger.handlers = access_logger.handlers if access_logger.handlers else logger.handlers
    return app


app = create_app()



