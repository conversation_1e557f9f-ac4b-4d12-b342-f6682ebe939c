# 这个是模版 -- 不需要修改
include:
  - project: 'open/gitlab-ci-template'
    ref: master
    file: 'template/backend-python-k8s.yaml'
# 这个项目的环境变量--要自己修改为自己项目对应的
variables:
  # 【修改】这个是服务名称
  APP_NAME: booking-edi-spider
  #这个可指定自己dockerfile路径，没有特殊需求 不需要修改  注释不需要放开
  DOCKERFILE_PATH: Dockerfile

# 如果你要修改指定环境对应的发布分支 请按照如下操作（注意：比之前版本多了 v2）
deploy-dev-v2:
#在这里指定分支规则即可，其他不需要写
  only:
   - /^feature.*$/
   - /^release.*$/
   - /^hotfix.*$/
deploy-beta-v2:
  only:
   - /^release.*$/
   - /^hotfix.*$/
deploy-uat-v2:
  only:
   - /^release.*$/
   - /^hotfix.*$/
deploy-prod-v2:
  only:
   - /^release.*$/
   - /^hotfix.*$/