import os
from urllib.parse import unquote

import gevent.monkey

from gunicorn_log import logconfig_dict
from conf import SERVER_ENV, PROJECT_PATH

gevent.monkey.patch_all()
# todo 单例模式初始化不要动态传入参数

loglevel = "INFO"
bind = "0.0.0.0:8090"
worker_class = "gevent"
workers = 1  # 启动的进程数
x_forwarded_for_header = "X-FORWARDED-FOR"

pidfile = os.path.join(PROJECT_PATH, "docs", "gunicorn.pid")

access_log_format = "%(h)s %(l)s '%(r)s' %(s)s %(L)s"

logconfig_dict = logconfig_dict


def pre_request(worker, req):
    # 将log日志输出显示中文
    req.uri = unquote(req.uri)
