#### MQ-TOPIC：
- smart-booking-python-booking-topic
#### MQ-TAG：
##### python-do-booking（python 订舱）
```java

/**
     * 订舱业务id
     */
private Long bookingId;

/**
     * 订舱详情
     */
private String detail;

/**
     * 日志id
     */
private Long recordId;

/**
     * 箱型
     */
private List<ContainerTypeMappingBean> containerTypes;

```
##### python-to-booking-result（python 订舱 回调）
```java

    /**
     * 日志记录业务主键id
     */
    private Long recordId;

    /**
     * bookingId
     */
    private Long bookingId;

    /**
     * 回填的订单号
     */
    private String billNo;

    /**
     * 提单号为空时，需要发送企业微信的信息
     */
    private String errorInfo;

    /**
     * 视频下载链接
     */
    private String videoDownloadUrl;

    /**
     * 视频展示链接
     */
    private String videoPreviewUrl;


    /**
     * 调用状态  true 成功  false  失败
     */
    private Boolean flag;

    /**
     * 是否保存草稿 true 是  false  否
     */
    private Boolean draftState;

    /**
     * 自定义查询结果
     */
    private String searchResult;

    /**
     * 委托编号
     */
    private String delegationNo;

    /**
     * 船司
     */
    private String carrierCode;

    /**
     * 供应商网站链接
     */
    private String website;

    /**
     * 供应商网站名称
     */
    private String websiteName;

    /**
     * python 订舱执行自动化的错误
     */
    private List<AutoErrorRecordBean> errors;
```
##### hh-edi-to-python（航华报文发送至python）
```java

    /**
     * 订舱业务id
     */
    private Long bookingId;

    /**
     * 订舱详情
     */
    private String detail;

    /**
     * 日志id
     */
    private Long recordId;

    /**
     * edi 报文内容
     */
    private String ediContent;
```
```
00:IFTMBF:BOOKING:9:SZHGJ:SINONBCT:202212291517:::2.0' 01:20221229119006639617' 10::EVER FORWARD 1091:010E:EVG:2023-01-07' 12:CNNGB:NINGBO:USYBS:BOSTON,NEW YORK,US:USYBS:BOSTON,NEW YORK,US' 14:SZHGJ:<EMAIL>:B:/SC91644::::CY_CY:P:FREIGHT PREPAID:SC91644::::::::' 20::SHENZHEN U-ZHEN INTERNATIONAL TRANSPORTATION CO.,LTD^ROOM 515,BUILDING A,BUSINESS CENTER, HUAFENG ZHIGU YUANSHAN HIGH-TECH PARK,HENGGANG STREET,LONGGANG DISTRICT,SHENZHEN,CHINA::::::::' 21:::C.H. ROBINSON^500 UNICORN PARK DRIVE SUITE 402 WOBURN, MA01801:::::::::::::' 22::::C.H. ROBINSON^500 UNICORN PARK DRIVE SUITE 402 WOBURN, MA01801::::::::' 40:1:45G1:1::F:::N' 41:001:9505900000::S:2000:CT:Carton:12000.00:68.000::::::::' 43::::::::::::::::::::::' 44:N/M' 47:TISSUE' 60::' 70:' 71::NINGBO:20221229:THREE::' 99:17'	
```
<a name="SZUft"></a>
##### hh-edi-to-python-result（航华报文发送至python 回调）
```java

    /**
     * 日志记录业务主键id
     */
    private Long recordId;

    /**
     * bookingId
     */
    private Long bookingId;

    /**
     * 回填的订单号
     */
    private String billNo;

    /**
     * 提单号为空时，需要发送企业微信的信息
     */
    private String errorInfo;

    /**
     * 视频下载链接
     */
    private String videoDownloadUrl;

    /**
     * 视频展示链接
     */
    private String videoPreviewUrl;


    /**
     * 调用状态  true 成功  false  失败
     */
    private Boolean flag;

    /**
     * 是否保存草稿 true 是  false  否
     */
    private Boolean draftState;

    /**
     * 自定义查询结果
     */
    private String searchResult;

    /**
     * 委托编号
     */
    private String delegationNo;

    /**
     * 船司
     */
    private String carrierCode;

    /**
     * 供应商网站链接
     */
    private String website;

    /**
     * 供应商网站名称
     */
    private String websiteName;

    /**
     * python 订舱执行自动化的错误
     */
    private List<AutoErrorRecordBean> errors;

```



