# -*- coding: utf-8 -*-
import os
import threading
from flask import abort, request

from server import app
from conf import PROJECT_PATH, SERVER_ENV
from utils.util_normal import send_message_to_developer

need_run_pyfile = [os.path.join(PROJECT_PATH, "spiders", "booking_start.py")]


@app.route('/')
@app.route('/actuator/health')
def health():
    """健康检查"""
    return {"code": 200}


@app.before_first_request
def before_first_request():
    """第一次运行时的操作"""
    t = threading.Thread(target=run_task, args=())
    t.start()


def run_task():
    contents = list()

    for pyfile in need_run_pyfile:
        command = f'nohup python3 {pyfile} 2>&1 &'
        app.logger.info(command)
        contents.append(SERVER_ENV + ' - ' + command)
        os.system(command)

    if contents:
        send_message_to_developer('\n'.join(contents).strip('\n'), [])


if __name__ == '__main__':
    app.run("0.0.0.0", 8090, debug=True)