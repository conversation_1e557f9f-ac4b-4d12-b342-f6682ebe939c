import os
import logging
from logging import LogRecord
from conf import PROJECT_PATH


class MessageFilter(logging.Filter):
    """过滤冗余请求"""
    def filter(self, record: LogRecord) -> int:
        msg = record.getMessage()
        if msg.find("/actuator/health") != -1 or msg.find("/actuator/prometheus") != -1 or msg.find("/favicon") != -1:
            return False
        return True


logconfig_dict = {
    "version": 1,
    "disable_existing_loggers": False,
    "root": {
          "level": "INFO",
          "handlers": ["console"]
    },
    "loggers": {
        "gunicorn.error": {
            "level": "INFO",
            "handlers": ["error_file"],
            "propagate": 0,
            "qualname": "gunicorn.error"
        },
        "urllib3.connectionpool": {
            "level": "ERROR",
            "handlers": ["access_file"],
            "propagate": 0,
            "qualname": "gunicorn.error"
        },
        "gunicorn.access": {
            "level": "INFO",
            "handlers": ["access_file"],
            "propagate": 1,
            "qualname": "gunicorn.access"
        }
    },
    "handlers": {
        "error_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "maxBytes": 1024*1024*10,
            "backupCount": 1,
            "encoding": "utf8",
            "formatter": "generic",
            "filename": os.path.join(PROJECT_PATH, "logs", "gunicorn.error.log")
        },
        "access_file": {
            "class": "logging.handlers.RotatingFileHandler",
            "maxBytes": 1024*1024*10,
            "backupCount": 1,
            "encoding": "utf8",
            "formatter": "generic",
            "filters": ["message_filter"],
            "filename": os.path.join(PROJECT_PATH, "logs", "server.log")
        },
        "console": {
            "class": "logging.StreamHandler",
            "level": "INFO",
            "filters": ["message_filter"],
            "formatter": "generic",
        },
    },
    "filters": {
        "message_filter": {
            "()": MessageFilter
        }
    },
    "formatters": {
        "generic": {
            "format": "%(levelname)s: %(asctime)s [%(module)s:%(lineno)d] %(message)s",
            "datefmt": "%Y-%m-%d %H:%M:%S",
            "class": "logging.Formatter"
        },
    }
}