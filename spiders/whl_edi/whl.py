"""
万海订舱：https://eshipping.wanhai.com/cec/#/mainpage
"""
import re
import time
import json
from io import Bytes<PERSON>
from typing import Optional
from threading import Thread
from urllib.parse import urljoin

from lxml.html import fromstring
from selenium.webdriver import Chrome
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from conf import NAME
from utils.util_rocketmq import ConnRocket
from utils import send_request
from utils.util_aes import aes_ecb_decrypt
from utils.util_normal import save_edi
from utils.util_normal import delete_edi_file
from spiders import Base, SeleniumOperateMixin, HandleMediaMixin
from utils.util_normal import handle_error_message
from utils.util_create_authorization import create_authorization
from conf import SERVER_ENV, SUBMIT_STATE, RECORD_STATE, EDI_UPLOAD_RESULT_URL, EDI_DIR, EDI_UPLOAD_TO_CHIPMUNK_RESULT_URL, WHL_ACCOUNT_CONFIG, MQ_TOPIC
from spiders.whl_edi.config import current_whl_username


class WHLSider(Base, SeleniumOperateMixin, HandleMediaMixin):

    name = "whl"

    def __init__(self, data, service, browser, file):
        Base.__init__(self, data)
        HandleMediaMixin.__init__(self, self.name)
        SeleniumOperateMixin.__init__(self)
        self.file = file
        self.msg = data
        self.service = service
        self.browser: Chrome = browser
        self.browser.set_window_size(width=1792, height=1120)
        self.params.delegationNo = data["detail"]["delegationNo"]
        self.params.carrierCode = data["detail"]["carrierCode"]
        self.booking_no = None
        self.edi_url = "https://es.wanhai.com/ces-api/#/BKG/e-Booking/EDINewBooking"
        self.booking_url = "https://es.wanhai.com/ces-api/#/BKG/e-Booking/BKGlist"
        self.wait = WebDriverWait(self.browser, 5, 0.2)
        self.wrapper = ""
        self.username = data["accountBean"]["account"] if data.get("accountBean") else WHL_ACCOUNT_CONFIG["username"]
        self.raw_password = data["accountBean"]["password"] if data.get("accountBean") else None
        self.password = aes_ecb_decrypt((data["recordId"], self.username), self.raw_password) if self.raw_password else WHL_ACCOUNT_CONFIG["password"]
        if not data.get("accountBean"):
            data["accountBean"] = {"account": self.username, "password": self.password}
        else:
            data["accountBean"]["password"] = self.password

    def check_data(self, *args, **kwargs):
        pass

    def before_insert(self):
        pass

    def insert_data(self, *args, **kwargs):
        pass

    def after_insert(self):
        pass

    def handle_result(self, *args, **kwargs):
        base_url = "https://dev-callback.hgj.com/new-ingress/booking-whl-edi-spider/{}"
        # 查看执行任务的队列个数
        count_url = base_url.format("getQueueSize")
        response = send_request(count_url, method="get")
        if response.json()["size"] >= 3:
            if self.msg.get("accountBean"):
                del self.msg["accountBean"]
            rocket = ConnRocket()
            rocket.produce_message(NAME, topic=MQ_TOPIC, message=json.dumps(self.msg, ensure_ascii=False, separators=(",", ":")), tags="whl-edi-to-python", delay_level=6)
            return
        url = base_url.format("whlEdiBooking")
        data = {
            "msg": self.msg,
            "server_env": SERVER_ENV
        }

        response = send_request(url, method="post", data=data, topic="json")
        self.logger.info(response.text)
