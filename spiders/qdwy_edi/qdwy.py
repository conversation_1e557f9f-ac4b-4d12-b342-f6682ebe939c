"""
青岛外运（箱满舱）自动订舱：https://www.etransful.com/home.html
需求文档：http://wiki.hgj.net/pages/viewpage.action?pageId=82973378
登录账号：18896718509
登录密码：Szhgj1234!
"""
import base64
import os
import time
import json
import datetime
from threading import Thread

from requests.cookies import RequestsCookieJar
from dateutil.relativedelta import relativedelta
from selenium.webdriver import Chrome
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from utils import create_authorization, logger
# from utils.util_rocketmq import ConnRocket
from spiders.hh_edi.configs import status_flag_dict
from spiders import Base, SeleniumOperateMixin, HandleMediaMixin, Params
from utils.util_normal import delete_edi_file, handle_error_message
from conf import MQ_CREATE_BILL_NO_TAG, RECORD_STATE, MQ_PRODUCER_ID, MQ_TOPIC, SERVER_ENV, \
    SUBMIT_STATE, NAS_DOWNLOAD_EXECL_URL
from utils.util_selenium import SeleniumBaseSpider


class QDWYSpider(Base, SeleniumOperateMixin, HandleMediaMixin):

    name = ""

    def __init__(self, data, service, browser):
        Base.__init__(self, data)
        HandleMediaMixin.__init__(self, self.name)
        SeleniumOperateMixin.__init__(self)
        self.msg = data
        self.service = service
        self.browser: Chrome = browser
        self.browser.set_window_size(width=1792, height=1120)
        self.file = None
        # self.params.delegationNo = data["detail"]["delegationNo"]
        # self.params.carrierCode = data["detail"]["carrierCode"]
        self.home_url = "https://www.etransful.com/home.html"
        self.upload_execl_api = "https://www.etransful.com/bmwCoreApi/bookingImport?ORG_ID=158"
        self.wait = WebDriverWait(self.browser, 10, 0.2)
        self.wrapper = ""
        self.execl_path = "temp.xls"
        # # 初始化网络监控
        # self.setup_network_monitoring()

    def setup_network_monitoring(self):
        """设置网络监控"""
        try:
            # 启用网络域
            self.browser.execute_cdp_cmd('Network.enable', {})
            # 启用运行时域
            self.browser.execute_cdp_cmd('Runtime.enable', {})
            self.logger.info("网络监控已启用")
        except Exception as e:
            self.logger.error(f"启用网络监控失败: {e}")


    def check_data(self, *args, **kwargs): ...

    def before_insert(self):
        # 根据环境变量开启录屏
        RECORD_STATE and Thread(target=self.screen_video_picture, args=(self.browser, self.params.delegationNo)).start()

    def insert_data(self, *args, **kwargs):
        if not self.login(): return  # todo: 登录
        if not self.handle_execl(): return  # todo: 上传execl
        if not self.insert_info():return  # todo: 填写信息
        if not self.submit():self.save_draft() # todo: 提交订舱/保存草稿
        self.check_result() # todo: 查询订舱结果

    def after_insert(self):...

    def handle_result(self, *args, **kwargs):
        self.screen_flag = False
        self.catch_exception()
        error_info_list = self.params.error_info_list
        error_info = ", ".join(error_info_list)
        # 系统有报错，或者填入时有报错才显示保存草稿失败
        flag = False if error_info_list else True

        if RECORD_STATE:
            self.params.download_url, self.params.preview_url = self.generate_video(self.params.delegationNo)
        else:
            self.params.download_url, self.params.preview_url = self.wrapper and self.upload_media(f"{self.name}_{self.params.delegationNo}.png", self.wrapper) or ("", "")

        if self.data["count"] == 2 and not flag:
            error_info = "".join([error_info, "  连续上传错误俩次"])

        message = {"bookingId": self.data["bookingId"], "recordId": self.data["recordId"], "errorInfo": error_info, "flag": flag, "videoDownloadUrl": not flag and self.params.download_url or "", "videoPreviewUrl": not flag and self.params.preview_url or "", "errors": self.params.error_detail}
        message.update(self.params.params)
        self.params.message = message
        mq_tag = MQ_CREATE_BILL_NO_TAG
        conn = ConnRocket()
        conn.produce_message(MQ_PRODUCER_ID, MQ_TOPIC, json.dumps(message), tags=mq_tag, arg=int(time.time()))
        # 保存订舱结果
        self.save_booking_result()

    @handle_error_message("error_info_system_list", "登陆失败", delay=1, tries=4)
    def login(self) -> bool:
        """登陆"""
        self.browser.get(self.home_url)
        # 点击登录
        login_btn = self.wait.until(EC.presence_of_element_located((By.XPATH, "//a[text()='登录']")))
        login_btn.click()
        # 点击密码登录
        login_btn = self.wait.until(EC.presence_of_element_located((By.XPATH, "//div[text()='密码登录']")))
        time.sleep(self.long_time)
        login_btn.click()
        # 输入账号密码
        username_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='text']")))
        password_input = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//input[@type='password']")))
        time.sleep(self.long_time)
        username_input.send_keys("18896718509",)
        time.sleep(self.long_time)
        password_input.send_keys("Szhgj1234!")
        # 点击登录
        login_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//buttun[@type='button']")))
        login_btn.click()
        self.get_captcha_img()
        self.handle_slider_captcha()

    @handle_error_message("error_info_system_list", "获取验证码请求失败")
    def get_network_responses(self):
        """获取验证码响应"""
        # 获取所有网络请求
        network_logs = self.browser.get_log('performance')
        for log in network_logs:
            message = json.loads(log['message'])
            # 只处理网络响应
            if message['message']['method'] == 'Network.responseReceived':
                response_data = message['message']['params']['response']
                request_id = message['message']['params']['requestId']
                # 过滤验证码请求URL
                if "https://online.sinotrans.com/api-common/iam-eas/v1/common/getImg" == response_data['url']:
                    # 获取响应体
                    response_body = self.browser.execute_cdp_cmd('Network.getResponseBody', {
                        'requestId': request_id
                    })
                    body_content = response_body.get('body', '')
                    if response_body.get('base64Encoded', False):
                        body_content = base64.b64decode(body_content).decode('utf-8')
                    response_info = {
                        'url': response_data['url'],

                    }
                    return response_info
        return False

    def get_captcha_img(self):
        """获取验证码图片"""
        # 获取网络响应
        responses = self.get_network_responses()
        # target_img,background_img =
        # return matching_responses


    def handle_slider_captcha(self,target_img,background_img):
        """滑块验证码处理方法"""
        try:
            # 等待验证码图片加载完成
            time.sleep(2)

            # 获取背景图和滑块图
            bg_img = self.browser.find_element(By.XPATH, "//div[contains(@class, 'captcha-bg')]//img")
            slider_img = self.browser.find_element(By.XPATH, "//div[contains(@class, 'captcha-slider')]//img")

            # 获取滑块按钮
            slider_btn = self.browser.find_element(By.XPATH, "//div[contains(@class, 'slider-btn')]")
            #计算距离
            det = ddddocr.DdddOcr(det=False, ocr=False)

            with open('target.png', 'rb') as f:
                target_bytes = f.read()

            with open('background.png', 'rb') as f:
                background_bytes = f.read()

            res = det.slide_match(target_bytes, background_bytes)

            print(res)
            # 暂时使用估算方法
            bg_width = bg_img.size['width']
            estimated_distance = bg_width * random.uniform(0.6, 0.8)  # 估算距离

            # 执行滑动操作
            actions = ActionChains(self.browser)
            actions.click_and_hold(slider_btn).perform()

            # 模拟真实的滑动轨迹
            current_distance = 0
            while current_distance < estimated_distance:
                # 计算下一步移动距离
                remaining_distance = estimated_distance - current_distance
                if remaining_distance > 20:
                    move_distance = random.uniform(8, 15)
                else:
                    move_distance = remaining_distance

                # 添加随机的垂直偏移
                y_offset = random.uniform(-2, 2)

                actions.move_by_offset(move_distance, y_offset).perform()
                current_distance += move_distance

                # 随机停顿
                time.sleep(random.uniform(0.01, 0.05))

            actions.release().perform()

            # 等待验证结果
            time.sleep(3)

            # 检查验证结果
            return self._check_slider_verification_result()

        except Exception as e:
            self.logger.error(f"高级滑块验证码处理失败：{str(e)}", exc_info=True)
            return False

    @handle_error_message("error_info_input_list", "上传execl失败", delay=1, tries=2)
    def handle_execl(self):
        """处理execl上传流程"""
        self.browser.get(self.home_url)
        # 点击在线订舱
        online_booking = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//a[text()='在线订舱']")))
        online_booking.click()

        # 选择子公司
        sub_company_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//button[@data-id='sub-company-id']")))
        sub_company_btn.click()
        huazhong_option = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//span[text()='华中集海']")))
        huazhong_option.click()

        # 选择船公司
        ship_company_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//button[@data-id='ship-company-id']")))
        ship_company_btn.click()
        msc_option = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//span[text()='MSC|地中海航运']")))
        msc_option.click()

        # 点击订舱按钮
        booking_btn = self.wait.until(EC.element_to_be_clickable((By.XPATH, ".//input[@value='订舱']")))
        booking_btn.click()

        # 等待页面加载完成
        self.wait.until(lambda driver: driver.execute_script("return document.readyState") == "complete")

        # 下载execl
        execl = self.download_execl("booking")
        if execl:
            with open(self.execl_path, "wb") as f:
                f.write(execl)
            return self.upload_execl()
        else:
            return False


    @handle_error_message("error_info_input_list", "下载execl失败", delay=1, tries=3)
    def download_execl(self,api_type):
        """下载execl"""
        headers = {"authorization": create_authorization(api_type)}
        params = {"":""}
        response = self.request(url=NAS_DOWNLOAD_EXECL_URL, method="get", headers=headers, params=params)
        if response.status_code == 200 and response.json()["code"] == 200:
            self.logger.info("下载execl成功")
            return response.content
        else:
            self.logger.error(f"下载execl失败，返回信息：{response.json()}")
            self.params.error_info_input_list.append("下载execl失败")
            return False

    @handle_error_message("error_info_input_list", "上传execl失败", delay=1, tries=3)
    def upload_execl(self):
        """上传execl"""
        # 检查本地文件是否存在
        if not os.path.exists(self.execl_path):
            self.logger.error("保存execl失败")
            self.params.error_info_input_list.append("保存execl失败")
            return False

        # 查找文件上传元素
        file_input = self.wait.until(EC.presence_of_element_located((By.ID, "uploadExcelBtn")))
        # 获取文件的绝对路径
        absolute_path = os.path.abspath(self.execl_path)
        # 使用selenium上传文件
        file_input.send_keys(absolute_path)
        time.sleep(self.long_time*3)
        if self.wait.until(EC.element_to_be_clickable((By.ID, "EBBO_CONSIGN_NO"))).text:
            self.logger.info("上传execl成功")
            return True
        else:
            self.logger.error(f"上传execl失败")
            self.params.error_info_input_list.append("上传execl失败")
            return False

    def save_booking_result(self):
        try:
            sql = "UPDATE hh_edi_booking_info set mediaDownloadLink=%s, mediaPreviewLink=%s, bookingEndTime=%s, message=%s where id=%s"
            self.util_mysql.update(sql, params=(self.params.download_url, self.params.preview_url, time.strftime("%Y-%m-%d %H:%M:%S"), json.dumps(self.params.message, ensure_ascii=False), self.insert_id))
        except Exception as e:
            self.logger.error(e, exc_info=True)

    def check_result(self):
        """检查订舱结果"""
        time.sleep(3)
        try:
            url = "https://hhdc.chinasailing.com.cn/m?xwl=MarkDept/onlineBooking/onlineBookingSql/selectOnlineBkVbillHead"
            today = datetime.datetime.today()
            today_str = today.isoformat().split('T')[0] + ' 00:00:00'
            one_month_ago = today + relativedelta(months=-3)
            one_month_ago_str = one_month_ago.isoformat().split('T')[0] + ' 00:00:00'
            payload = {
                'start': '0',
                'limit': '30',
                'billStartDate': one_month_ago_str,
                'frtNo': self.params.delegationNo,
                'vesselCompany': [self.params.carrierCode],
                'billEndDate': today_str
            }

            jar = RequestsCookieJar()
            [jar.set(one["name"], one["value"]) for one in self.browser.get_cookies()]
            response = self.request(url, method="post", data=payload, topic="form", cookies=jar)
            json_data = response.json()
            search_result = ""
            is_exist = False
            if json_data["total"] > 0:
                rows = json_data["rows"]
                search_result = '，'.join([f"{adict['BILL_NO']}-{status_flag_dict[str(adict['STATE_FLAG'])]}" for adict in rows if self.params.billNo == adict['BILL_NO'].strip()])
                is_exist = any([True for adict in rows if adict["CLIENT_NBR"] == self.params.delegationNo and str(adict['STATE_FLAG']) in ["1", "2", "3", "6"]])
            return search_result, is_exist
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("查询订舱结果失败")
            return "", ""

    def save_draft(self):
        """保存草稿"""
        try:
            self.close_tips()
            # 保存草稿
            self.browser.find_element(By.XPATH, "/html/body/div[7]//span[text()='保存草稿']/ancestor::a").click()
            # 设置草稿保存状态
            self.params.draftState = True
            self.is_not_need_retry = True
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append(f"委托编号：{self.params.delegationNo}保存草稿失败")

    def submit(self):
        """保存并提交"""
        try:
            if not self.params.error_info_input_list and ((SERVER_ENV == "prod" and not self.data["detail"]["bookingOrderInfoBean"]["cargoInfoBeanList"][0]["dangerType"]) or SUBMIT_STATE):
                # 提交
                self.is_not_need_retry = True
                self.browser.find_element(By.XPATH, "/html/body/div[7]//span[text()='保存并提交']/ancestor::a").click()
                time.sleep(self.long_time*2)
                notify_info = self.browser.find_element(By.XPATH, f'/html/body/div[{self.find_visibility_window()}]/div[2]/div/div/div[1]/div/div/div[2]/span/div/table[1]/tbody/tr/td[2]').text
                if notify_info:
                    self.params.error_info_input_list.append("提单号自动生成错误")
                else:
                    return True
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("已提交订舱，航华网站更新延迟，请复核。")
        return False



    def __del__(self):
        self.screen_flag = False
        self.wrapper and self.wrapper.close()
        delete_edi_file(self.file)




if __name__ == '__main__':
    selenium_obj = SeleniumBaseSpider(logger)
    # 是否需要开启无头模式，默认是开启
    selenium_obj.headless = False
    browser, service = selenium_obj.create_browser()
    q = QDWYSpider("",service,browser)
    q.login()
