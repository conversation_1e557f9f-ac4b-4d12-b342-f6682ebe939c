"""
自动化订舱（报文）
"""

import time
import json

from spiders.hh_edi import __all__ as hh_spiders
from qdwy_edi.qdwy import QDWYSpider
from whl_edi.whl import WHLSider
from utils import logger, send_message_to_developer
from utils.util_redis import RedisUtil
from utils.util_normal import save_edi
from utils.util_rocketmq import ConnRocket
from utils.util_selenium import SeleniumBase<PERSON>pider
from conf import MQ_SUBSCRIBE_ID, MQ_TOPIC, MQ_SUBSCRIBE_TAG, EDI_DIR


selenium_obj = SeleniumBaseSpider(logger)
# # 是否需要开启无头模式，默认是开启
# selenium_obj.headless = False


class HandleMQ(object):

    def __init__(self):
        self.rocket = ConnRocket()

    def go(self):
        self.rocket.push_message(
            group_id=MQ_SUBSCRIBE_ID,
            topic=MQ_TOPIC,
            func=self.deal,
            expression=MQ_SUBSCRIBE_TAG
        )

    @staticmethod
    def deal(msg):
        # 每次重试2次
        count = 1
        origin_msg = None
        while count < 3:
            browser, service = selenium_obj.create_browser()
            try:
                if not isinstance(msg, dict):
                    origin_msg = msg
                    message_id = msg.id
                    msg = json.loads(msg.body)
                    msg["messageId"] = message_id
                else:
                    msg["messageId"] = str(int(time.time()*1000))

                # 将运行次数字段设置到数据中
                msg["count"] = count

                # 校验redis，避免mq在订舱成功后重试
                carrier_code = msg['detail']['carrierCode']
                name = f"booking-edi-spider:{carrier_code}"
                is_had = RedisUtil().prevent_repeat(msg["messageId"], name)
                # 如果在redis查到mq的messageId并且不是失败重试的，暂停执行订舱
                if is_had and count == 1:
                    logger.warning(f"重试单子被拦截-{msg['messageId']}")
                    break

                # 开始订舱
                if carrier_code in hh_spiders and ((origin_msg and "hh-edi-to-python" == origin_msg.tags.decode()) or True):
                    # 保存报文到本地
                    file_path = save_edi(msg, EDI_DIR, name=msg['detail']['delegationNo'] + ".txt")
                    edi_obj = hh_spiders[carrier_code](msg, service=service, browser=browser, file=file_path)
                    edi_obj.main()
                elif carrier_code.lower() == "whl" and ((origin_msg and "whl-edi-to-python" == origin_msg.tags.decode()) or True):
                    edi_obj = WHLSider(msg, service=service, browser=browser, file=None)
                    edi_obj.main()
                elif carrier_code.lower() == "qdwy":
                    edi_obj = QDWYSpider(msg, service=service, browser=browser)
                    edi_obj.main()
                else:
                    send_message_to_developer(f"船公司：{carrier_code}未开通提交报文功能", [])
                    break
                if edi_obj.is_not_need_retry or (not edi_obj.params.error_info_system_list and not edi_obj.params.error_info_input_list): break
                del edi_obj
            except Exception as e:
                logger.error(e, exc_info=True)
                carrier_code = msg['detail']['carrierCode']
                delegation_no = msg['detail']['delegationNo']
                send_message_to_developer(f"{carrier_code}-{delegation_no}订舱失败", [])
            finally:
                count += 1


if __name__ == '__main__':
    # msg1 = {"bookingId": "1120229726024449", "detail": {"bookingId": "1120229726024449", "businessBookingId": "1120229726024449", "businessPortCode": "SHANGHAI", "portNameEn": "SHANGHAI", "delegationNo": "SH25074412", "billNo": "", "masterBillNo": "", "carrierCode": "WHL", "carrierNameEn": "WHL", "carrierNameCn": "\u4e07\u6d77\u822a\u8fd0", "bookingStatus": "SENT_TO_SHIPPING_COMPANY_FOR_RELEASE", "bookingOrderInfoBean": {"shipperContactInfoBean": {"shipperName": "U.S.UNITED LOGISTICS (NINGBO) INC.", "shipperAddress": "20TH FLOOR,3BLDG,NO.1926,CANGHAI\nROAD,SHANGDONG BUSINESS CENTRE\nJIANGDONG DISTRICT,ZHEJIANG,CHINA", "shipperCountry": None, "shipperCountryCode": None, "shipperProvince": None, "shipperProvinceCode": None, "shipperCity": None, "shipperCityCode": None, "shipperPostcode": None}, "consigneeContactInfoBean": {"consigneeName": "RI-TIME LOGISTICS CORP", "consigneeAddress": "2707 E. VALLEY BLVD #306,\nWEST COVINA, CA 91792\nTEL: ************ FAX: ************\<EMAIL>", "consigneeCountry": None, "consigneeCountryCode": None, "consigneeProvince": None, "consigneeProvinceCode": None, "consigneeCity": None, "consigneeCityCode": None, "consigneePostcode": None}, "notifyContactInfoBean": {"notifyName": "RI-TIME LOGISTICS CORP", "notifyAddress": "2707 E. VALLEY BLVD #306,\nWEST COVINA, CA 91792\nTEL: ************ FAX: ************\<EMAIL>", "notifyCountry": None, "notifyCountryCode": None, "notifyProvince": None, "notifyProvinceCode": None, "notifyCity": None, "notifyCityCode": None, "notifyPostcode": None}, "secondNotifyContactInfoBean": {"secondNotifyName": None, "secondNotifyAddress": "", "secondNotifyCountry": None, "secondNotifyCountryCode": None, "secondNotifyProvince": None, "secondNotifyProvinceCode": None, "secondNotifyCity": None, "secondNotifyCityCode": None, "secondNotifyPostcode": None}, "bookingBaseInfoBean": {"delegationNo": "SH25074412", "billNo": "", "shippingCompanyCode": "WHL", "signingMethod": "OBL", "billFNum": 3, "billSNum": 3, "offSiteMark": None, "offSitePlace": "", "offSitePlaceCode": "", "paymentMethod": "FP", "paymentPlaceCode": "", "paymentPlace": "", "transportClause": "CY_CY", "contractNo": "NGB25-326N", "mrCodeFront": "", "mrCodeEnd": None, "oneselfSendingFlag": "HGJ", "scacCode": "UULN", "aci": "8F0D", "house": None, "nameAccount": None, "bookingRemark": "SCAC CODE:UULN", "motNo": None, "motNoMark": None}, "sailingScheduleInfoBean": {"vessel": "HMM OPAL", "voyageNo": "0006E", "routeCode": "1", "etd": "2025-07-12", "placeOfReceipt": "SHANGHAI", "placeOfReceiptCode": "CNSHA", "portOfLoading": "SHANGHAI", "portOfLoadingCode": "CNSHA", "portOfTranshipment": None, "portOfTranshipmentCode": None, "portOfDischarge": "OAKLAND,CALIFORNIA,US", "portOfDischargeCode": "USOAK", "placeOfDelivery": "OAKLAND,CALIFORNIA,US", "placeOfDeliveryCode": "USOAK", "destination": None, "destinationCode": None, "placeOfDeliveryVoyageLine": "\u7f8e\u52a0\u7ebf", "placeOfDeliveryVoyageLineCode": "CNUSC"}, "cargoInfoBeanList": [{"genericType": True, "coldStorageType": None, "dangerType": None, "oogType": None, "chemicalsType": None, "marks": "N/M", "englishProductName": "LUNCH-BOX\nSCAC CODE:UULN\nNO SOLID WOOD PACKING \nMATERIAL", "chineseProductName": None, "number": 500, "packingUnit": "PK", "packageUnitNameEn": "PACKAGE", "packageUnitNameCn": "\u5305", "grossWeight": 15000, "netWeight": None, "volume": 28, "hsCode": "3924100000", "temperature": None, "temperatureUnit": None, "maximumTemperature": None, "minimumTemperature": None, "ventSwitch": None, "ventilationRate": None, "dangerClass": None, "dangerUnCode": None, "urgentContactPerson": "", "urgentContactPhone": "", "page": None, "dangerLabel": None, "dangerFlashPoint": None, "dangerEmn": None, "megn": None, "marinePollution": None, "casNo": None, "subClass": None, "properShippingName": None, "domesticWarehouseName": None, "frontBeyond": None, "retralBeyond": None, "leftBeyond": None, "rightBeyond": None, "heightBeyond": None, "oggUnit": "CM"}], "containerInfoBeanList": [{"containerType": "20GP", "containerTeu": 1, "containerNumber": 1, "soc": False, "ownersContainerType": None, "containerMark": "FULL", "avgGrossWeight": None}], "additionalInfoBean": None, "contactInfoBean": {"contactPerson": "139****1289", "phone": "13917771289", "qq": None, "emails": "<EMAIL>"}, "bookingOtherInfoBean": {"bookingNo": "1121235676856833", "poNo": None, "shippingCompanySales": None, "freightRateDesc": None}, "bookingFileBeans": None}, "version": 1, "mergeFlag": "MASTER", "supplierId": "", "carrierSupplierId": "18", "createTime": "2025-07-02 10:26:25", "enterpriseId": "8133938", "enterpriseName": "\u5b81\u6ce2\u7f8e\u822a\u7269\u6d41\u6709\u9650\u516c\u53f8\u4e0a\u6d77\u5206\u516c\u53f8", "bookingType": "APPOINTMENT_NUM"}, "recordId": "1120240538100737", "ediContent": "00:IFTMBF:BOOKING:9:HGJ:WHL:202507021110'\n02:1121235676856833::CY-CY::WH:::N:N:::NGB25-326N:::NGB25-326N::'\n03:EXP:CNSHA:SHANGHAI:20210804:3::'\n11:0006E:HMM OPAL:0006E::::::::::'\n12:CNSHA:SHANGHAI:CNSHA:SHANGHAI:USOAK:OAKLAND,CALIFORNIA,US:::USOAK:OAKLAND,CALIFORNIA,US::'\n14:P:FREIGHT PREPAID'\n17:$$***********$***********::::<EMAIL>:3924100000::::LUNCH-BOXSCAC CODE?:UULNNO SOLI^D WOOD PACKING MATERIAL#REJIAN^GDONG DISTRICT,ZHEJIANG,:::::'\n20::U.S.UNITED LOGISTICS (NINGBO) ^INC.:20TH FLOOR,3BLDG,NO.1926,CANGH^AIROAD,SHANGDONG BUSINESS CENT#::::'\n21::RI-TIME LOGISTICS CORP:2707 E. VALLEY BLVD #306,WEST ^COVINA, CA 91792TEL?: 626-839-0^968 FAX?: ************IMPORT@RI^TIMELOGISTICS.COM::::'\n22::RI-TIME LOGISTICS CORP:2707 E. VALLEY BLVD #306,WEST ^COVINA, CA 91792TEL?: 626-839-0^968 FAX?: ************IMPORT@RI^TIMELOGISTICS.COM::::'\n23:::::::::::::'\n24::::::::::::::'\n25::::::::::::::'\n41:1::S:500:PK:PACKAGE:15000.000:28.000::::::15000.000:0.000::'\n44:N/M'\n47:LUNCH-BOXSCAC CODE?:UULNNO SOLI^D WOOD PACKING MATERIAL#REJIAN^GDONG DISTRICT,ZHEJIANG,:LUNCH-BOXSCAC CODE?:UULNNO SOLI^D WOOD PACKING MATERIAL#REJIAN^GDONG DISTRICT,ZHEJIANG,'\n48:22G1:1:F:::::N:'\n99:18'\n\n\n\n", "containerTypes": None, "bookingExtendData": None, "messageId": "7F00000100061698C449078C2573880B", "count": 1}
    mq = HandleMQ()
    mq.go()
    # for msg in [msg1]:
    #     mq.deal(msg)

