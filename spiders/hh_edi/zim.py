import time
from functools import partial

from spiders.hh_edi.hh_base import HHBase

from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains


# 订舱信息
BOOKING_CONNECT = "MARCOS"
BOOKING_TEL = "15026780891"
BOOKING_MAIL = "<EMAIL>"


class HSDSpider(HHBase):

    name = "ZIM"

    def insert_base_info(self):
        """填写基本信息, 拼接提单号
        东南亚线（且目的地港口五码不为MY开头的）或非洲线用GOSUSNH拼接在前面。
        目的地港口五码以MY开头的，用LNLUSNH拼接，
        除此之外用ZIMUSNH
        """
        super().insert_base_info()
        line_name = self.data["bookingExtendData"]["placeOfDeliveryVoyageLineName"]
        sailing_schedule_info = self.data["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]
        if sailing_schedule_info["placeOfDeliveryCode"].startswith("MY"):
            self.params.billNo = "LNLUSNH" + self.params.billNo if not self.params.billNo.startswith("LNLUSNH") else self.params.billNo
        else:
            if line_name.strip() in ["东南亚线", "非洲线"]:
                self.params.billNo = "GOSUSNH" + self.params.billNo if not self.params.billNo.startswith("GOSUSNH") else self.params.billNo
            else:
                self.params.billNo = "ZIMUSNH" + self.params.billNo if not self.params.billNo.startswith("ZIMUSNH") else self.params.billNo
        self.clear_and_send_keys(self.browser, "xpath", "//fieldset[1]//label[contains(text(), '提单号')]/parent::td/following-sibling::td/input", self.params.billNo)

    def insert_vessel_info(self):
        """填写船舶信息"""
        try:
            sail_schedule_info = self.data["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]  # 港口信息
            self.close_tips()
            a_ele = self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(), '船名')]/ancestor::table/following-sibling::a")
            self.browser.execute_script("arguments[0].click();", a_ele)
            self.wait.until(partial(self.wait_to_clickable, xpath="//span[text()='选船']/ancestor::div//label[text()='船名']/parent::td/following-sibling::td/input"))
            self.clear_and_send_keys(self.browser, "xpath", "//span[text()='选船']/ancestor::div//label[text()='船名']/parent::td/following-sibling::td/input", sail_schedule_info["vessel"])
            self.clear_and_send_keys(self.browser, "xpath", "//span[text()='选船']/ancestor::div//label[text()='航次']/parent::td/following-sibling::td/input", sail_schedule_info["voyageNo"])
            # 清除结束时间
            self.browser.find_element(By.XPATH, "//span[text()='选船']/ancestor::div//label[contains(text(), '结束时间')]/parent::td/following-sibling::td//input").clear()
            # 点击查找按钮
            self.browser.find_element(By.XPATH, "//span[text()='选船']/ancestor::div//span[text()='查找' or text()='Find']/ancestor::a")
            time.sleep(self.mid_time)
            trs = self.browser.find_elements(By.XPATH, "//span[text()='选船']/ancestor::div/div[2]/div/div/div/div/div/div[2]/div[2]/div/table/tbody/tr")
            for index, tr in enumerate(trs):
                tds = tr.find_elements(By.XPATH, "./td")
                if tds[1].text == sail_schedule_info["vessel"] and tds[3].text == sail_schedule_info["voyageNo"]:
                    ActionChains(self.browser).double_click(trs[index]).perform()
                    break
            else:
                # 手动输入船期数据
                self.close_tips()
                checkbox_class = self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(),  '是否临时船期')]/following-sibling::table//input[not(contains(@class, 'x-form-cb-after'))]/ancestor::table").get_attribute("class")
                "x-form-cb-checked" not in checkbox_class and self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(),  '是否临时船期')]/following-sibling::table//input[not(contains(@class, 'x-form-cb-after'))]").click()
                self.clear_and_send_keys(self.browser, "xpath", "/html/body//fieldset[2]//label[contains(text(),  '船名')]/parent::td/following-sibling::td/input", sail_schedule_info["vessel"])
                self.clear_and_send_keys(self.browser, "xpath", "/html/body//fieldset[2]//label[contains(text(),  '航次')]/parent::td/following-sibling::td/input", sail_schedule_info["voyageNo"])
                self.clear_and_send_keys(self.browser, "xpath", "/html/body//fieldset[2]//label[contains(text(),  '开航日')]/parent::td/following-sibling::td//input", sail_schedule_info["etd"])
                time.sleep(self.short_time)
                try:
                    notify_info = self.browser.find_element(By.XPATH, f"/html/body/div[{self.find_visibility_window()}]/div[2]/div/div/div[1]/div/div/div[2]/span/div/table[1]/tbody/tr/td[2]/div").text
                    notify_info and self.close_tips()
                    notify_info and self.params.error_info_input_list.append(notify_info)
                except:
                    pass
            self.close_tips()
            # 填入航线
            sail_schedule_info["routeCode"] and self.select_option(params={
                "value": sail_schedule_info["routeCode"],
                "input_xpath": "//fieldset[1]//label[contains(text(), '航线')]/parent::td/following-sibling::td//tr/td[1]//input",
                "down_xpath": "//fieldset[1]//label[contains(text(), '航线')]/parent::td/following-sibling::td//tr/td[2]/div",
                "element_type": "tr",
                "match_value": sail_schedule_info["routeCode"],
                "error_info": f'航线代码：{sail_schedule_info["routeCode"]}未找到'
            }, dynamic=False)
            not sail_schedule_info and self.params.error_info_input_list.append("航线代码为空")
        except Exception as e:
            self.close_tips()
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("船舶信息填入失败")

    def insert_other_info(self):
        try:
            def select_contact_info(params):
                for one in self.browser.find_elements(By.XPATH, f"/html/body/div[{self.get_div_count()}]//{params['element_type']}"):
                    if params["match_value"] == one.text.strip():
                        one.click()
                        return True
                else:
                    self.clear_and_send_keys(self.browser, "xpath", params["input_xpath"], params["value"])

            # 付费地
            booking_base_info = self.data["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]
            sailing_schedule_info = self.data["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]
            payment_place, payment_place_code = booking_base_info["paymentPlace"], booking_base_info["paymentPlaceCode"]
            if not payment_place and not payment_place_code:
                if booking_base_info["paymentMethod"] in ['FP', 'FPATP']:
                    payment_place, payment_place_code = sailing_schedule_info["portOfLoading"], sailing_schedule_info["portOfLoadingCode"]
                else:
                    payment_place, payment_place_code = sailing_schedule_info["placeOfDelivery"], sailing_schedule_info["placeOfDeliveryCode"]
            self.select_option(params={
                "value": payment_place.split(",", 1)[0],
                "input_xpath": "/html/body/div[7]//label[contains(text(), '付费地')]/parent::td/following-sibling::td//tr/td[1]/input",
                "down_xpath": "/html/body/div[7]//label[contains(text(), '付费地')]/parent::td/following-sibling::td//tr/td[2]/div",
                "element_type": "tr",
                "match_value": payment_place_code,
                "error_info": f'付费地：{payment_place}未找到'
            })

            # 订舱联系人
            self.select_option(params={
                "value": BOOKING_CONNECT,
                "input_xpath": "/html/body/div[7]//label[contains(text(), '订舱联系人')]/parent::td/following-sibling::td//tr/td[1]/input",
                "down_xpath": "/html/body/div[7]//label[contains(text(), '订舱联系人')]/parent::td/following-sibling::td//tr/td[2]/div",
                "element_type": "tr",
                "match_value": BOOKING_CONNECT,
                "error_info": f"订舱联系人获取失败：{BOOKING_CONNECT}"
            }, func=select_contact_info)
            # 订舱电话
            if self.browser.find_element(By.XPATH, "/html/body/div[7]//label[contains(text(), '订舱电话')]/parent::td/following-sibling::td//tr/td[1]/input").get_attribute("value") != BOOKING_TEL:
                self.select_option(params={
                    "value": BOOKING_TEL,
                    "input_xpath": "/html/body/div[7]//label[contains(text(), '订舱电话')]/parent::td/following-sibling::td//tr/td[1]/input",
                    "down_xpath": "/html/body/div[7]//label[contains(text(), '订舱电话')]/parent::td/following-sibling::td//tr/td[2]/div",
                    "element_type": "tr",
                    "match_value": BOOKING_TEL,
                    "error_info": f"订舱电话获取失败：{BOOKING_TEL}"
                }, func=select_contact_info)
            # 订舱邮箱
            if self.browser.find_element(By.XPATH, "/html/body/div[7]//label[contains(text(), '订舱邮箱')]/parent::td/following-sibling::td//tr/td[1]/input").get_attribute("value").upper() != BOOKING_MAIL:
                self.select_option(params={
                    "value": BOOKING_MAIL,
                    "input_xpath": "/html/body/div[7]//label[contains(text(), '订舱邮箱')]/parent::td/following-sibling::td//tr/td[1]/input",
                    "down_xpath": "/html/body/div[7]//label[contains(text(), '订舱邮箱')]/parent::td/following-sibling::td//tr/td[2]/div",
                    "element_type": "tr",
                    "match_value": BOOKING_MAIL,
                    "error_info": f"订舱邮箱获取失败：{BOOKING_MAIL}"
                }, func=select_contact_info)
        except Exception as e:
            self.params.error_info_input_list.append("其他信息填写失败")
            self.logger.error(e, exc_info=True)
