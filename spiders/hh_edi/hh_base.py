"""
航华报文提交
航华网站：
"""
import re
import time
import json
import datetime
from io import BytesIO
from functools import partial
from threading import Thread

from lxml.html import fromstring
from selenium.common.exceptions import TimeoutException
from requests.cookies import RequestsCookieJar
from dateutil.relativedelta import relativedelta
from selenium.webdriver import Chrome
from selenium.webdriver.common.by import By
from selenium.webdriver import ActionChains
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

# from utils.util_rocketmq import ConnRocket
from spiders.hh_edi.configs import status_flag_dict
from spiders import Base, SeleniumOperateMixin, HandleMediaMixin
from utils.util_normal import delete_edi_file
from conf import HH_ACCOUNT_CONFIG, EDI_DIR, MQ_CREATE_BILL_NO_TAG, RECORD_STATE, MQ_PRODUCER_ID, MQ_TOPIC, SERVER_ENV, SUBMIT_STATE


class HHBase(Base, SeleniumOperateMixin, HandleMediaMixin):

    name = ""

    def __init__(self, data, service, browser, file):
        Base.__init__(self, data)
        HandleMediaMixin.__init__(self, self.name)
        SeleniumOperateMixin.__init__(self)
        self.msg = data
        self.service = service
        self.browser: Chrome = browser
        self.browser.set_window_size(width=1792, height=1120)
        self.file = file
        self.params.delegationNo = data["detail"]["delegationNo"]
        self.params.carrierCode = data["detail"]["carrierCode"]
        self.url = "https://hhdc.chinasailing.com.cn/home"
        self.wait = WebDriverWait(self.browser, 5, 0.2)
        self.wrapper = ""

    def login(self) -> bool:
        """登陆航华网站"""
        try:
            self.browser.get("https://hhdc.chinasailing.com.cn/login")
            if self.browser.current_url == "https://hhdc.chinasailing.com.cn/home":
                return True
            self.wait.until(EC.element_to_be_clickable((By.ID, "username")))
            self.clear_and_send_keys(self.browser, 'id', 'username', HH_ACCOUNT_CONFIG["username"])
            self.clear_and_send_keys(self.browser, 'id', 'password', HH_ACCOUNT_CONFIG["password"])
            captcha = self.browser.find_element(By.ID, 'imgcaptan').screenshot_as_png
            rsp = self.ff.predict(pred_type='30400', img_data=captcha, src_url='http://sh.chinasailing.com.cn/')
            self.clear_and_send_keys(self.browser, 'id', 'captan', rsp.value)
            self.browser.find_element(By.XPATH, "//button[text()='登录']").click()
            self.wait.until(lambda b: b.current_url == "https://hhdc.chinasailing.com.cn/home")
        except Exception as e:
            self.logger.error(e, exc_info=True)
            return False
        return True

    def enter_submit_edi_page(self):
        """进入到报文提交页面"""
        time.sleep(self.long_time)
        try:
            self.close_tips()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[text()='海运订舱管理']//ancestor::a"))).click()
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//a[text()='网上订舱']"))).click()
            return True
        except Exception as e:
            self.logger.error(e, exc_info=True)
            return False

    def submit_edi(self):
        try:
            WebDriverWait(self.browser, 10, 0.2).until(partial(self.wait_to_clickable, xpath="//span[text()='订舱报文导入']//ancestor::a"))
            self.wait.until(lambda x: "选择文件" in x.page_source)
            # 显示文件名称
            el = self.wait.until(EC.visibility_of_element_located((By.XPATH, "//label[text()='选择文件']/preceding-sibling::table//input")))
            self.browser.execute_script('arguments[0].readOnly=false', el)
            el.send_keys(self.params.delegationNo + ".txt")
            el = self.browser.find_element(By.XPATH, "//input[@name='file1']")
            el.send_keys(self.file)
            self.wait.until(partial(self.wait_to_clickable, xpath=f"//span[text()='订舱报文导入']/ancestor::div//span[text()='确定' or text()='OK']//ancestor::a"))
            return True
        except Exception as e:
            self.params.error_info_system_list.append("报文提交失败")
            self.logger.error(e, exc_info=True)
            return False

    def search_result(self):
        """检查"""
        time.sleep(3)
        try:
            url = "https://hhdc.chinasailing.com.cn/m?xwl=MarkDept/onlineBooking/onlineBookingSql/selectOnlineBkVbillHead"
            today = datetime.datetime.today()
            today_str = today.isoformat().split('T')[0] + ' 00:00:00'
            one_month_ago = today + relativedelta(months=-3)
            one_month_ago_str = one_month_ago.isoformat().split('T')[0] + ' 00:00:00'
            payload = {
                'start': '0',
                'limit': '30',
                'billStartDate': one_month_ago_str,
                'frtNo': self.params.delegationNo,
                'vesselCompany': [self.params.carrierCode],
                'billEndDate': today_str
            }

            jar = RequestsCookieJar()
            [jar.set(one["name"], one["value"]) for one in self.browser.get_cookies()]
            response = self.request(url, method="post", data=payload, topic="form", cookies=jar)
            json_data = response.json()
            search_result = ""
            is_exist = False
            if json_data["total"] > 0:
                rows = json_data["rows"]
                search_result = '，'.join([f"{adict['BILL_NO']}-{status_flag_dict[str(adict['STATE_FLAG'])]}" for adict in rows if self.params.billNo == adict['BILL_NO'].strip()])
                is_exist = any([True for adict in rows if adict["CLIENT_NBR"] == self.params.delegationNo and str(adict['STATE_FLAG']) in ["1", "2", "3", "6"]])
            return search_result, is_exist
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("查询订舱结果失败")
            return "", ""

    def check_submit_result(self):
        time.sleep(5)
        # 查询条件
        try:
            # 设置订舱时间为今天
            self.clear_and_send_keys(self.browser, "xpath", "//label[text()='订舱时间']/parent::td/following-sibling::td//input", time.strftime("%Y-%m-%d"))
            # 选择船公司
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//label[text()='船公司']/parent::td/following-sibling::td//tr/td[1]/input"))).send_keys(self.name)
            time.sleep(self.long_time)
            divs = self.browser.find_elements(By.XPATH, '/html/body/div')
            self.wait.until(partial(self.wait_to_clickable, xpath=f"/html/body/div[{len(divs)}]//ul/li[1]"))
            # 选择草稿
            self.wait.until(partial(self.wait_to_clickable, xpath="//label[text()='草稿']/preceding-sibling::input"))
            # 查询
            self.wait.until(EC.element_to_be_clickable((By.XPATH, "//span[text()='查询']/../../..")))
            time.sleep(self.long_time)
            self.wait.until(lambda x: len(x.find_elements(By.XPATH, "/html/body/div[7]//span[contains(text(), '序号')]/ancestor::div[contains(@class, 'x-box-inner')]/parent::div/following-sibling::div[1]//table//tr")) >= 1)
            time.sleep(self.long_time)
        except Exception as e:
            self.params.error_info_input_list.append("多条件查询未找到草稿数据，请确认")
            self.logger.error(e, exc_info=True)
            return False
        return True

    def select_current_row(self):
        """选择符合条件的一条数据"""
        try:
            # 获取标题列表，根据标题列表取值
            title_list = self.access_element_location()
            # 选中符合条件的数据
            for tr in self.browser.find_elements(By.XPATH, "/html/body/div[7]//span[contains(text(), '序号')]/ancestor::div[contains(@class, 'x-box-inner')]/parent::div/following-sibling::div[1]//table//tr"):
                self.browser.find_elements(By.XPATH, "/html/body/div[7]//span[contains(text(), '序号')]/ancestor::div[contains(@class, 'x-box-inner')]/parent::div/following-sibling::div[1]//table//tr")
                # 校验船名航次是否一致
                sailing_schedule_info = self.msg["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]
                # 船名
                vessel = tr.find_element(By.XPATH, f"./td[{title_list.index('船名') + 1}]").text.strip()
                # 航次
                voyage = tr.find_element(By.XPATH, f"./td[{title_list.index('航次') + 1}]").text.strip()
                # 委托编号
                delegationNo = tr.find_element(By.XPATH, f"./td[{title_list.index('委托编号') + 1}]").text.strip()
                if sailing_schedule_info["vessel"] == vessel and sailing_schedule_info["voyageNo"] == voyage and not delegationNo:
                    ActionChains(self.browser).double_click(tr).perform()
                    # 查看订舱备注委托编号是否一致
                    ref_no = self.wait.until(EC.element_to_be_clickable((By.XPATH, "//label[contains(text(), '订舱要求')]/parent::td/following-sibling::td//textarea"))).get_attribute("value").upper().rsplit("REF NO:", 1)[-1]
                    if ref_no == self.params.delegationNo.upper():
                        return True
                    else:
                        self.browser.find_element(By.XPATH, "/html/body/div[7]//span[contains(text(), '基本信息')]/ancestor::a").click()
                        time.sleep(self.long_time)
            else:
                raise ValueError("未找到符合条件的数据")
        except Exception as e:
            self.params.error_info_input_list.append("报文上传失败，未找到符合条件的数据")
            self.logger.error(e, exc_info=True)
            return False

    def insert_base_info(self):
        """填写基本信息"""
        try:
            self.params.billNo = self.browser.find_element(By.XPATH, "//fieldset[1]//label[contains(text(), '提单号')]/parent::td/following-sibling::td/input").get_attribute("value")
            self.clear_and_send_keys(self.browser, "xpath", "//fieldset[1]//label[text()='委托编号']/parent::td/following-sibling::td/input", self.params.delegationNo)
        except Exception as e:
            self.params.error_info_input_list.append("基本信息填写失败")
            self.logger.error(e, exc_info=True)

    def insert_vessel_info(self): ...

    def insert_notify_info(self): ...

    def insert_other_info(self): ...

    def insert_cargo_info(self): ...

    def insert_container_info(self): ...

    def insert_cold_info(self): ...

    def insert_danger_info(self): ...

    def insert_oog_info(self): ...

    def save_draft(self):
        """保存草稿"""
        try:
            self.close_tips()
            # 保存草稿
            self.browser.find_element(By.XPATH, "/html/body/div[7]//span[text()='保存草稿']/ancestor::a").click()
            # 设置草稿保存状态
            self.params.draftState = True
            self.is_not_need_retry = True
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append(f"委托编号：{self.params.delegationNo}保存草稿失败")

    def submit(self):
        """保存并提交"""
        try:
            if not self.params.error_info_input_list and ((SERVER_ENV == "prod" and not self.data["detail"]["bookingOrderInfoBean"]["cargoInfoBeanList"][0]["dangerType"]) or SUBMIT_STATE):
                # 提交
                self.is_not_need_retry = True
                self.browser.find_element(By.XPATH, "/html/body/div[7]//span[text()='保存并提交']/ancestor::a").click()
                time.sleep(self.long_time*2)
                notify_info = self.browser.find_element(By.XPATH, f'/html/body/div[{self.find_visibility_window()}]/div[2]/div/div/div[1]/div/div/div[2]/span/div/table[1]/tbody/tr/td[2]').text
                if notify_info:
                    self.params.error_info_input_list.append("提单号自动生成错误")
                else:
                    return True
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("已提交订舱，航华网站更新延迟，请复核。")
        return False

    # 工具
    @staticmethod
    def wait_to_clickable(browser: Chrome, xpath):
        try:
            browser.find_element(By.XPATH, xpath).click()
            return True
        except:
            return False

    def find_visibility_window(self):
        """查找点击保存并提交之后的显示窗口"""
        max_z_index = 0
        order = 0
        for index, div in enumerate(self.browser.find_elements(By.XPATH, "/html/body/div"), 1):
            # 查看z-index等级
            style = div.get_attribute("style")
            z_index = "display: none" not in style and "z-index" in style and int(style.split("z-index:")[-1].split(";")[0]) or 0
            if z_index > max_z_index:
                max_z_index = z_index
                order = index
        return order

    def close_tips(self):
        """自动关闭提示"""
        time.sleep(self.long_time)
        divs = self.browser.find_elements(By.XPATH, "/html/body/div")
        max_z_index = 0
        xpath_list = []
        # 找出所有弹框，并按照z-index进行排序
        for index, div in enumerate(divs, 1):
            img_button = div.find_elements(By.XPATH, './/img[contains(@class, "x-tool-close")]')
            if img_button:
                # 查看z-index等级
                style = div.get_attribute("style")
                z_index = "z-index" in style and int(style.split("z-index:")[-1].split(";")[0]) or 0
                if z_index > max_z_index:
                    xpath_list.insert(0, f'//div[{index}]//img[contains(@class, "x-tool-close")]')
                else:
                    xpath_list.append(f'//div[{index}]//img[contains(@class, "x-tool-close")]')
                max_z_index = z_index
        # 按照弹框的先后顺序进行关闭
        for close_button in xpath_list:
            try:
                WebDriverWait(self.browser, 3, 0.3).until(EC.presence_of_element_located((By.XPATH, close_button))).click()
            except:
                pass

    def select_option(self, params: dict, dynamic=True, func=None, count=None) -> bool:
        """
        点击下拉内容
        :param params: 参数，{value:"需要填入的值", input_xpath: "输入框xpath", down_xpath: "下拉按钮xpath", element_type: "下拉框元素类型", match_value: "需要匹配的值", error_info: "错误信息"}
        :param func: 处理的函数
        :param dynamic: 查看函数是否是动态加载的数据，动态的需要刷新
        :param count: 传入元素的位置
        :return: bool
        """
        for i in range(2):
            try:
                # 将选择框上划
                self.browser.execute_script("arguments[0].scrollIntoView();", self.browser.find_element(By.XPATH, params["input_xpath"]))
                # 1.清空输入框内容
                self.browser.find_element(By.XPATH, params["input_xpath"]).clear()
                # 2.点击下拉框
                self.browser.find_element(By.XPATH, params["down_xpath"]).click()
                # 3.校验下拉框是否弹出
                self.wait.until(lambda driver: "display" not in driver.find_element(By.XPATH, f"/html/body/div[{count or len(self.browser.find_elements(By.XPATH, '/html/body/div'))}]").get_attribute("style"))
                # 4. 再次清空输入框内容
                self.browser.find_element(By.XPATH, params["input_xpath"]).clear()
                # 5.输入内容
                self.browser.find_element(By.XPATH, params["input_xpath"]).send_keys(params["value"])
                # 6.查看是否有符合的选中项, 没有将输入框清空
                try:
                    not RECORD_STATE and (dynamic and WebDriverWait(self.browser, 3, 0.1).until(EC.staleness_of(self.browser.find_element(By.XPATH, f"/html/body/div[{count or self.get_div_count()}]//{params['element_type']}"))) or time.sleep(self.short_time)) or time.sleep(self.mid_time)
                except TimeoutException:
                    pass
                if func:
                    return func(params)
                else:
                    for one in self.browser.find_elements(By.XPATH, f"/html/body/div[{count or self.get_div_count()}]//{params['element_type']}"):
                        if params["match_value"].upper() in one.text.upper():
                            one.click()
                            return True
            except Exception as e:
                self.logger.warning(e, exc_info=True)
            # 清除输入框
            self.browser.find_element(By.XPATH, params["input_xpath"]).clear()
        else:
            self.params.error_info_input_list.append(params["error_info"])
            return False

    def access_element_location(self):
        """获取标题列表，只获取display可见的"""
        ele = fromstring(self.browser.page_source)
        title_ele_list = ele.xpath("/html/body/div[7]//span[contains(text(), '序号')]/ancestor::div[contains(@class, 'x-box-inner')]/div/div[not(contains(@style, 'display'))]//span")
        title_list = list(map(lambda x: "".join(x.xpath("./text()")).strip(), title_ele_list))
        return title_list

    def get_div_count(self) -> int:
        """获取div的数量"""
        count = len(self.browser.find_elements(By.XPATH, "/html/body/div"))
        return count

    def catch_exception(self):
        """抓取当前页面异常"""
        try:
            labels = self.browser.find_elements(By.XPATH, "//input[contains(@class, ' x-form-invalid-field')]/ancestor::table/tbody/tr/td[1]/label|//textarea[contains(@class, ' x-form-invalid-field')]/ancestor::table/tbody/tr/td[1]/label")
            for one in labels:
                label = one.text.strip()
                label = re.sub("[*:：]*", "", label).strip()
                label and self.params.error_info_check_list.append(label)
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_system_list.append("页面检查失败")

    def save_booking_result(self):
        try:
            sql = "UPDATE hh_edi_booking_info set mediaDownloadLink=%s, mediaPreviewLink=%s, bookingEndTime=%s, message=%s where id=%s"
            self.util_mysql.update(sql, params=(self.params.download_url, self.params.preview_url, time.strftime("%Y-%m-%d %H:%M:%S"), json.dumps(self.params.message, ensure_ascii=False), self.insert_id))
        except Exception as e:
            self.logger.error(e, exc_info=True)

    def check_data(self, *args, **kwargs): ...

    def before_insert(self):
        # 根据环境变量开启录屏
        RECORD_STATE and Thread(target=self.screen_video_picture, args=(self.browser, self.params.delegationNo)).start()

    def insert_data(self, *args, **kwargs):
        # 登陆和船公司选择重试3次
        login_state = False
        select_state = False
        for i in range(3):
            login_state = self.login()
            login_state and self.browser.refresh()
            select_state = login_state and self.enter_submit_edi_page()
            if all([login_state, select_state]): break
        else:
            not login_state and self.params.error_info_system_list.append("登陆失败")
            login_state and not select_state and self.params.error_info_system_list.append("请确认网站状态是否正常！")
            return False

        # 上传报文
        if not self.submit_edi(): return
        # 查验报文是否已经上传成功
        if not self.check_submit_result(): return
        # 查验是否有满足条件的数据
        if not self.select_current_row(): return
        # 填充数据
        self.insert_base_info()
        self.insert_vessel_info()
        self.insert_notify_info()
        self.insert_other_info()
        self.insert_cargo_info()
        self.insert_container_info()
        self.insert_cold_info()
        self.insert_danger_info()
        self.insert_oog_info()
        self.save_screen()
        not self.submit() and self.save_draft()
        search_result, _ = self.search_result()
        if search_result:
            self.params.searchResult = search_result

    def save_screen(self):
        """填入完成后截取全屏"""
        try:
            self.wrapper and self.wrapper.close()
            if "保存并提交" in self.browser.page_source and EC.visibility_of_element_located((By.XPATH, "/html/body/div[7]//span[contains(text(), '保存并提交')]"))(self.browser):
                box_height = self.browser.find_element(By.XPATH, "/html/body/div[7]/div[2]/div[2]/div/div/div/div/div/div[2]/div[2]").size["height"]
                content_height = self.browser.find_element(By.XPATH, "/html/body/div[7]/div[2]/div[2]/div/div/div/div/div/div[2]/div[2]/div[2]/span/div").size["height"]
                height = self.browser.get_window_size()["height"] - box_height + content_height
                self.browser.set_window_size(width=self.browser.get_window_size()["width"], height=height)
                p = self.browser.find_element(By.XPATH, "/html/body/div[7]/div[2]/div[2]/div/div/div/div/div/div[2]/div[2]/div[2]/span/div").screenshot_as_png
                self.wrapper = BytesIO(p)
                self.browser.set_window_size(width=1792, height=1120)
            else:
                p = self.browser.find_element(By.CSS_SELECTOR, "html").screenshot_as_png
                self.wrapper = BytesIO(p)
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.wrapper = ""

    def after_insert(self):
        # 当填入失败时再次截图，方便将漏填字段标红显示保留
        (self.params.error_info_input_list or self.params.error_info_system_list) and self.save_screen()

    def handle_result(self, *args, **kwargs):
        self.screen_flag = False
        self.catch_exception()
        error_info_list = self.params.error_info_list
        error_info = ", ".join(error_info_list)
        # 系统有报错，或者填入时有报错才显示保存草稿失败
        flag = False if error_info_list else True

        if RECORD_STATE:
            self.params.download_url, self.params.preview_url = self.generate_video(self.params.delegationNo)
        else:
            self.params.download_url, self.params.preview_url = self.wrapper and self.upload_media(f"{self.name}_{self.params.delegationNo}.png", self.wrapper) or ("", "")

        if self.data["count"] == 2 and not flag:
            error_info = "".join([error_info, "  连续上传错误俩次"])

        message = {"bookingId": self.data["bookingId"], "recordId": self.data["recordId"], "errorInfo": error_info, "flag": flag, "videoDownloadUrl": not flag and self.params.download_url or "", "videoPreviewUrl": not flag and self.params.preview_url or "", "errors": self.params.error_detail}
        message.update(self.params.params)
        self.params.message = message
        mq_tag = MQ_CREATE_BILL_NO_TAG
        conn = ConnRocket()
        conn.produce_message(MQ_PRODUCER_ID, MQ_TOPIC, json.dumps(message), tags=mq_tag, arg=int(time.time()))
        # 保存订舱结果
        self.save_booking_result()

    def __del__(self):
        self.screen_flag = False
        self.wrapper and self.wrapper.close()
        delete_edi_file(self.file)
