"""
向外暴漏爬虫类
"""

import pkgutil
import inspect

from spiders.hh_edi.hh_base import HHBase

classes = {}

for loader, name, is_pkg in pkgutil.walk_packages(__path__):
    module = loader.find_module(name).load_module(name)
    for class_name, value in inspect.getmembers(module):
        if inspect.isclass(value) and issubclass(value, HHBase) and value is not HHBase and value.name:
            classes[value.name] = value


__all__ = classes
