import time
import json
import datetime

from requests.cookies import Requests<PERSON><PERSON><PERSON><PERSON><PERSON>
from dateutil.relativedelta import relativedelta
from spiders.hh_edi.hh_base import HHBase
from selenium.webdriver import ActionChains
from selenium.webdriver.common.by import By
from selenium.webdriver.support import expected_conditions as EC

from spiders.hh_edi.configs import status_flag_dict
# from utils.util_rocketmq import ConnRocket
from conf import MQ_CREATE_BILL_NO_TAG, RECORD_STATE, MQ_PRODUCER_ID, MQ_TOPIC


# 订舱信息
BOOKING_CONNECT = "KEVIN"
BOOKING_TEL = "13621824573"
BOOKING_MAIL = "<EMAIL>"


class HSDSpider(HHBase):

    name = "HSD"

    def insert_vessel_info(self):
        """填写船舶信息"""
        try:
            sail_schedule_info = self.data["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]  # 港口信息

            order = self.get_div_count() + 1
            self.close_tips()
            self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(), '船名')]/ancestor::table/following-sibling::a").click()
            not RECORD_STATE and self.wait.until(EC.visibility_of_element_located((By.XPATH, f"/html/body/div[{order}]"))) or time.sleep(self.short_time)
            self.clear_and_send_keys(self.browser, "xpath", f"/html/body/div[{self.get_div_count()}]//label[contains(text(), '船名')]/parent::td/following-sibling::td/input", sail_schedule_info["vessel"])
            self.clear_and_send_keys(self.browser, "xpath", f"/html/body/div[{self.get_div_count()}]//label[contains(text(), '航次')]/parent::td/following-sibling::td/input", sail_schedule_info["voyageNo"])
            # 清除结束时间
            self.browser.find_element(By.XPATH, f"/html/body/div[{self.get_div_count()}]//label[contains(text(), '结束时间')]/parent::td/following-sibling::td//input").clear()
            # 点击查找按钮
            self.browser.find_element(By.XPATH, f"/html/body/div[{self.get_div_count()}]//span[text()='查找' or text()='Find']/ancestor::a")
            time.sleep(self.mid_time)
            trs = self.browser.find_elements(By.XPATH, f"/html/body/div[{self.get_div_count()}]/div[2]/div/div/div/div/div/div[2]/div[2]/div/table/tbody/tr")
            for index, tr in enumerate(trs):
                tds = tr.find_elements(By.XPATH, "./td")
                if tds[1].text == sail_schedule_info["vessel"] and tds[3].text == sail_schedule_info["voyageNo"]:
                    ActionChains(self.browser).double_click(trs[index]).perform()
                    break
            else:
                # 未找到船期数据需要将当前数据清空
                self.close_tips()
                self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(),  '是否临时船期')]/following-sibling::table//input[not(contains(@class, 'x-form-cb-after'))]").click()
                self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(),  '船名')]/parent::td/following-sibling::td/input").clear()
                self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(),  '航次')]/parent::td/following-sibling::td/input").clear()
                self.browser.find_element(By.XPATH, "/html/body//fieldset[2]//label[contains(text(),  '开航日')]/parent::td/following-sibling::td//input").clear()
            self.close_tips()
        except Exception as e:
            self.close_tips()
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("船舶信息填入失败")

    def insert_other_info(self):
        try:
            def select_contact_info(params):
                for one in self.browser.find_elements(By.XPATH, f"/html/body/div[{self.get_div_count()}]//{params['element_type']}"):
                    if params["match_value"] == one.text.strip():
                        one.click()
                        return True
                else:
                    self.clear_and_send_keys(self.browser, "xpath", params["input_xpath"], params["value"])

            booking_base_info = self.data["detail"]["bookingOrderInfoBean"]["bookingBaseInfoBean"]
            sailing_schedule_info = self.data["detail"]["bookingOrderInfoBean"]["sailingScheduleInfoBean"]

            payment_method = booking_base_info["paymentMethod"]
            payment_place = ""
            payment_place_code = ""
            if payment_method == "FP":
                payment_place = sailing_schedule_info["portOfLoading"]
                payment_place_code = sailing_schedule_info["portOfLoadingCode"]
            elif payment_method == "FC":
                payment_place = sailing_schedule_info["placeOfDelivery"]
                payment_place_code = sailing_schedule_info["placeOfDeliveryCode"]
            elif payment_method in ["FPATP", "FCATP"]:
                payment_place = booking_base_info["paymentPlace"]
                payment_place_code = booking_base_info["paymentPlaceCode"]

            # 付费地
            if all([payment_place, payment_place_code]):
                self.select_option(params={
                    "value": payment_place,
                    "input_xpath": "/html/body/div[7]//label[contains(text(), '付费地')]/parent::td/following-sibling::td//tr/td[1]/input",
                    "down_xpath": "/html/body/div[7]//label[contains(text(), '付费地')]/parent::td/following-sibling::td//tr/td[2]/div",
                    "element_type": "tr",
                    "match_value": payment_place_code,
                    "error_info": f'付费地：{payment_place}未找到'
                }, func=select_contact_info)

            # 订舱联系人
            self.select_option(params={
                "value": BOOKING_CONNECT,
                "input_xpath": "/html/body/div[7]//label[contains(text(), '订舱联系人')]/parent::td/following-sibling::td//tr/td[1]/input",
                "down_xpath": "/html/body/div[7]//label[contains(text(), '订舱联系人')]/parent::td/following-sibling::td//tr/td[2]/div",
                "element_type": "tr",
                "match_value": BOOKING_CONNECT,
                "error_info": f"订舱联系人获取失败：{BOOKING_CONNECT}"
            }, func=select_contact_info)
            # 订舱电话
            if self.browser.find_element(By.XPATH, "/html/body/div[7]//label[contains(text(), '订舱电话')]/parent::td/following-sibling::td//tr/td[1]/input").get_attribute("value") != BOOKING_TEL:
                self.select_option(params={
                    "value": BOOKING_TEL,
                    "input_xpath": "/html/body/div[7]//label[contains(text(), '订舱电话')]/parent::td/following-sibling::td//tr/td[1]/input",
                    "down_xpath": "/html/body/div[7]//label[contains(text(), '订舱电话')]/parent::td/following-sibling::td//tr/td[2]/div",
                    "element_type": "tr",
                    "match_value": BOOKING_TEL,
                    "error_info": f"订舱电话获取失败：{BOOKING_TEL}"
                }, func=select_contact_info)
            # 订舱邮箱
            if self.browser.find_element(By.XPATH, "/html/body/div[7]//label[contains(text(), '订舱邮箱')]/parent::td/following-sibling::td//tr/td[1]/input").get_attribute("value").upper() != BOOKING_MAIL:
                self.select_option(params={
                    "value": BOOKING_MAIL,
                    "input_xpath": "/html/body/div[7]//label[contains(text(), '订舱邮箱')]/parent::td/following-sibling::td//tr/td[1]/input",
                    "down_xpath": "/html/body/div[7]//label[contains(text(), '订舱邮箱')]/parent::td/following-sibling::td//tr/td[2]/div",
                    "element_type": "tr",
                    "match_value": BOOKING_MAIL,
                    "error_info": f"订舱邮箱获取失败：{BOOKING_MAIL}"
                })
        except Exception as e:
            self.params.error_info_input_list.append("其他信息填写失败")
            self.logger.error(e, exc_info=True)

    def search_result(self):
        """检查"""
        time.sleep(3)
        try:
            url = "https://hhdc.chinasailing.com.cn/m?xwl=MarkDept/onlineBooking/onlineBookingSql/selectOnlineBkVbillHead"
            today = datetime.datetime.today()
            today_str = today.isoformat().split('T')[0] + ' 00:00:00'
            one_month_ago = today + relativedelta(months=-3)
            one_month_ago_str = one_month_ago.isoformat().split('T')[0] + ' 00:00:00'
            payload = {
                'start': '0',
                'limit': '30',
                'billStartDate': one_month_ago_str,
                'frtNo': self.params.delegationNo,
                'vesselCompany': [self.params.carrierCode],
                'billEndDate': today_str
            }

            jar = RequestsCookieJar()
            [jar.set(one["name"], one["value"]) for one in self.browser.get_cookies()]
            response = self.request(url, method="post", data=payload, topic="form", cookies=jar)
            json_data = response.json()
            search_result = ""
            is_exist = False
            if json_data["total"] > 0:
                rows = json_data["rows"]
                search_result = [True for adict in rows if self.params.delegationNo == adict['CLIENT_NBR'].strip() and adict['STATE_FLAG'] == "0"]
                is_exist = any([True for adict in rows if adict["CLIENT_NBR"] == self.params.delegationNo and str(adict['STATE_FLAG']) in ["1", "2", "3", "6"]])
            return search_result, is_exist
        except Exception as e:
            self.logger.error(e, exc_info=True)
            self.params.error_info_input_list.append("查询订舱结果失败")
            return "", ""

    def handle_result(self, *args, **kwargs):
        self.screen_flag = False
        self.catch_exception()
        error_info_list = self.params.error_info_list
        error_info = ", ".join(error_info_list)
        # 系统有报错，或者填入时有报错才显示保存草稿失败
        flag = True if self.params.searchResult else False
        # 将查询结果置为空
        self.params.searchResult = ""

        if RECORD_STATE:
            self.params.download_url, self.params.preview_url = self.generate_video(self.params.delegationNo)
        else:
            self.params.download_url, self.params.preview_url = self.wrapper and self.upload_media(f"{self.name}_{self.params.delegationNo}.png", self.wrapper) or ("", "")

        if self.data["count"] == 2 and not flag:
            error_info = "".join([error_info, "  连续上传错误俩次"])

        message = {"bookingId": self.data["bookingId"], "recordId": self.data["recordId"], "errorInfo": error_info, "flag": flag, "videoDownloadUrl": not flag and self.params.download_url or "", "videoPreviewUrl": not flag and self.params.preview_url or "", "errors": self.params.error_detail}
        message.update(self.params.params)
        self.params.message = message
        mq_tag = MQ_CREATE_BILL_NO_TAG
        conn = ConnRocket()
        conn.produce_message(MQ_PRODUCER_ID, MQ_TOPIC, json.dumps(message), tags=mq_tag, arg=int(time.time()))
        # 保存订舱结果
        self.save_booking_result()

