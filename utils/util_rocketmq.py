"""
操作RocketMQ
"""
import json
import time
import datetime
import traceback

from retry.api import retry
from rocketmq.client import Producer, Message, PushConsumer

from conf import ROCKET_MQ_CONFIG, SERVER_ENV
from utils import send_message_to_developer


class ConnRocket(object):

    _instance = ""
    _producer = None

    def __new__(cls, *args, **kwargs):
        if cls._instance:
            return cls._instance
        else:
            cls._instance = super(ConnRocket, cls).__new__(cls, *args, **kwargs)
            return cls._instance

    def __init__(self, addr=None):
        if not addr:
            addr = ROCKET_MQ_CONFIG
        self.addr = addr
        self.tags = "booking"

    @retry(tries=2)
    def produce_message(self, group_id, topic, message, tags=None, arg=None, delay_level=None) -> tuple:
        """
        生产消息
        :param group_id: group_id
        :param topic: topic
        :param message: 消息
        :param tags: 设置的tag
        :param arg: 唯一参数
        :param delay_level: 延时级别
        :return:
        """
        try:
            if not self._producer:
                producer = Producer(group_id)
                producer.set_namesrv_addr(self.addr)
                producer.start()
                self._producer = producer
            mes_obj = Message(topic=topic)
            mes_obj.set_keys(str(datetime.datetime.now()))
            mes_obj.set_tags(tags if tags else self.tags)
            message = json.dumps(message, ensure_ascii=False) if isinstance(message, dict) else message
            mes_obj.set_body(message)
            delay_level and mes_obj.set_delay_time_level(delay_level)
            if arg:
                result = self._producer.send_orderly(mes_obj, arg)
            else:
                result = self._producer.send_sync(mes_obj)
            return result.status, result.msg_id, result.offset
        except Exception as e:
            self._producer.shutdown()
            message = traceback.format_exc()
            send_message_to_developer(str(message), [])
            return "", "", ""

    def push_message(self, group_id, topic, func, expression="*"):
        """
        消费消息
        :param group_id: group_id
        :param topic: topic
        :param func: 调用的方法
        :param expression: 可以订阅tag，example："TagA || TagC || TagD"
        :return:
        """
        consumer = PushConsumer(group_id=group_id)
        consumer.set_namesrv_addr(self.addr)
        consumer.subscribe(topic=topic, callback=func, expression=expression)
        consumer.set_thread_count(1)
        consumer.set_message_batch_max_size(1)      # 一次只消费一个消息
        consumer.start()
        while True:
            time.sleep(30)

