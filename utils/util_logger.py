import os
import sys
import logging
import traceback
import importlib.util

# from gunicorn.config import Config
# from gunicorn.instrument.statsd import Statsd

from conf import GUNICORN_FILENAME
from hgj_loguru import hgj_logger

# class GenerateLogger:
#
#     def __init__(self):
#         self.cfg = Config()
#
#     def get_config_from_filename(self):
#         ext = os.path.splitext(GUNICORN_FILENAME)[1]
#         module_name = '__config__'
#         if ext in [".py", ".pyc"]:
#             spec = importlib.util.spec_from_file_location("gunicorn.conf", GUNICORN_FILENAME)
#             mod = importlib.util.module_from_spec(spec)
#             sys.modules[module_name] = mod
#             spec.loader.exec_module(mod)
#             for k, v in vars(mod).items():
#                 if k not in self.cfg.settings:
#                     continue
#                 try:
#                     self.cfg.set(k.lower(), v)
#                 except Exception:
#                     print("Invalid value for %s: %s\n" % (k, v), file=sys.stderr)
#                     sys.stderr.flush()
#                     raise
#         else:
#             print("Failed to read config file: %s" % GUNICORN_FILENAME, file=sys.stderr)
#             traceback.print_exc()
#             sys.stderr.flush()
#             sys.exit(1)
#
#     def __call__(self, *args, **kwargs):
#         self.get_config_from_filename()
#         logger = Statsd(self.cfg).access_log
#         return logger


# 实例化并调用
logger: logging.Logger = hgj_logger
