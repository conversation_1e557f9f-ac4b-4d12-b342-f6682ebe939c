import smtplib
from email.mime.text import MIMEText
from email.header import <PERSON><PERSON>

from utils import send_message_to_developer


class SendMail(object):

    def __init__(self, *args, **kwargs):
        self.user = kwargs.get("user", "")
        self.password = kwargs.get("password", "")
        self.host = kwargs.get("host", "")
        self.port = kwargs.get("port", "")
        self.subtype = kwargs.get("subtype", "html")

    @staticmethod
    def __format_addr(contact: list):
        for one in contact:
            name, host = one.split("@")
            yield f"{name}<{one}>"

    def send(self, contents, subject, to: list):
        try:
            contents = contents.replace("\n", "<br>")
            message = MIMEText(contents, self.subtype, "utf-8")
            message["From"] = ",".join(self.__format_addr([self.user]))
            message["to"] = ",".join(self.__format_addr(to))
            message["Subject"] = Head<PERSON>(subject, "utf-8")

            smtp_obj = smtplib.SMTP_SSL(host=self.host, port=self.port)
            smtp_obj.login(user=self.user, password=self.password)
            smtp_obj.sendmail(self.user, to, message.as_string())
        except:
            msg = ": ".join(["邮件发送失败", "主题 - " + subject])
            send_message_to_developer(msg, [])


if __name__ == '__main__':
    mail_config = {
        "user": "<EMAIL>",
        "password": "Booking1@",
        "host": "smtp.yunlsp.com",
        "port": 465
    }
    s = SendMail(**mail_config)
    s.send(contents="<h1>Python邮件发送测试...</h1><h2>测试</h2>",
           subject="Python SMTP 邮件测试",
           to=["<EMAIL>"]
           )
