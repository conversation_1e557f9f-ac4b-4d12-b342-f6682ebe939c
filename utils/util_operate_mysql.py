"""
操作数据库
"""
import pymysql
from dbutils.pooled_db import PooledDB
from conf import MYSQL_CONFIG as sql_conf


class MysqlUtil(object):
    _instance = None
    __pool = None

    def __new__(cls, *args, **kwargs):
        if cls._instance:
            return cls._instance
        else:
            cls._instance = object.__new__(cls)
            return cls._instance

    def __init__(self, *args, **kwargs):
        sql_conf.update(kwargs)
        self.host = sql_conf.get("host")
        self.port = sql_conf.get("port")
        self.user = sql_conf.get("user")
        self.password = sql_conf.get("password")
        self.db = sql_conf.get("db")
        self.charset = sql_conf.get("charset")

        self._conn = self.create_conn()
        self._cursor = self._conn.cursor()

    def create_conn(self):
        if self.__pool is None:
            self.__pool = PooledDB(pymysql, maxcached=3, maxshared=5, maxconnections=5,
                                   host=self.host, user=self.user, password=self.password,
                                   database=self.db, port=self.port, charset=self.charset
                                   )

        return self.__pool.connection()

    def get_all(self, sql, params=None):
        count = self._cursor.execute(sql, params)
        if count > 0:
            result = self._cursor.fetchall()
        else:
            result = ()
        return result

    def get_one(self, sql, params=None) -> tuple:
        count = self._cursor.execute(sql, params)
        if count > 0:
            result = self._cursor.fetchone()
        else:
            result = ()
        return result

    def update(self, sql, params):
        count = self._cursor.execute(sql, params)
        self._conn.commit()
        primary_key_id = self._cursor.lastrowid
        return count, primary_key_id

    def __del__(self):
        self.__pool.close()
        self._conn.close()
        self._cursor.close()


if __name__ == '__main__':
    def test():
        # sql = "INSERT INTO hh_edi_booking_info (carrierCode, delegationNo, data) VALUES (%s, %s, %s)"
        sql = "UPDATE hh_edi_booking_info set mediaDownloadLink=%s, mediaPreviewLink=%s, bookingStartTime=%s where id=%s"
        s = MysqlUtil()
        res = s.update(sql, ("http://www.baidu.com", "http://www.baidu.com", "2023-07-20 14:09:15", 6))
        return res

    print(test())
